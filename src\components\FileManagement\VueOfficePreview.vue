<template>
  <div class="vue-office-preview">
    <!-- 加载状态 -->
    <div v-if="loading" class="preview-loading">
      <el-icon class="is-loading" :size="48">
        <Loading />
      </el-icon>
      <p>正在加载预览...</p>
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="error" class="preview-error">
      <el-icon :size="48" color="#F56C6C">
        <Warning />
      </el-icon>
      <p>{{ error }}</p>
    </div>
    
    <!-- Excel缩放控制 -->
    <div v-if="previewType === 'excel'" class="excel-toolbar">
      <div class="excel-zoom-controls">
        <el-button-group size="small">
          <el-button :icon="ZoomOut" @click="adjustExcelZoom(-0.1)" :disabled="excelZoom <= 0.5">
            缩小
          </el-button>
          <el-button @click="resetExcelZoom">
            {{ Math.round(excelZoom * 100) }}%
          </el-button>
          <el-button :icon="ZoomIn" @click="adjustExcelZoom(0.1)" :disabled="excelZoom >= 2">
            放大
          </el-button>
        </el-button-group>
        <span class="zoom-tip">调整缩放以适应内容大小</span>
      </div>
    </div>

    <!-- Vue Office 预览组件 -->
    <div class="office-preview-container" :class="`preview-${previewType}`" :style="excelContainerStyle">
      <!-- Word 文档预览 -->
      <vue-office-docx
        v-if="previewType === 'docx' && documentSrc && isReady"
        :key="`docx-${props.fileName || 'unknown'}-${renderKey}`"
        :src="documentSrc"
        :style="previewStyle"
        class="docx-viewer"
        @rendered="handleRendered"
        @error="handleError"
      />

      <!-- Excel 文档预览 -->
      <vue-office-excel
        v-else-if="previewType === 'excel' && documentSrc && isReady"
        :key="`excel-${props.fileName || 'unknown'}-${renderKey}`"
        :src="documentSrc"
        :style="previewStyle"
        :options="excelOptions"
        class="excel-viewer"
        @rendered="handleRendered"
        @error="handleError"
      />

      <!-- PDF 文档预览 -->
      <vue-office-pdf
        v-else-if="previewType === 'pdf' && documentSrc && isReady"
        :key="`pdf-${props.fileName || 'unknown'}-${renderKey}`"
        :src="documentSrc"
        :style="previewStyle"
        class="pdf-viewer"
        :options="pdfOptions"
        @rendered="handleRendered"
        @error="handleError"
      />

      <!-- PowerPoint 文档预览 -->
      <div v-else-if="previewType === 'pptx' && documentSrc && isReady" class="pptx-container">
        <vue-office-pptx
          :key="`pptx-${props.fileName || 'unknown'}-${renderKey}`"
          :src="documentSrc"
          :style="previewStyle"
          class="pptx-viewer"
          @rendered="handlePptxRendered"
          @error="handleError"
        />

        <!-- PowerPoint专用加载覆盖层 -->
        <div v-if="pptxLoading" class="pptx-loading-overlay">
          <div class="pptx-loading-content">
            <el-icon class="is-loading" :size="48" color="#409EFF">
              <Loading />
            </el-icon>
            <h3>正在加载PowerPoint演示文稿</h3>
            <p>请稍候，PowerPoint文件正在渲染中...</p>
            <div class="loading-progress">
              <el-progress :percentage="pptxProgress" :show-text="false" />
            </div>
          </div>
        </div>
      </div>

      <!-- 等待文档加载完成时显示空白，不显示加载状态 -->
      <div v-else-if="loading || !isReady || (previewType !== 'unsupported' && !documentSrc)" class="waiting-content">
        <!-- 空白区域，等待文档加载完成 -->
      </div>

      <!-- 真正不支持的文件类型 -->
      <div v-else class="unsupported-preview">
        <el-icon :size="64" color="#909399">
          <Document />
        </el-icon>
        <h3>暂不支持预览此文件类型</h3>
        <p>支持的格式：Word (.docx, .doc)、Excel (.xlsx, .xls)、PDF (.pdf)、PowerPoint (.pptx, .ppt)</p>
        <p>请检查文件格式是否正确或联系管理员</p>
        <p class="file-info">当前文件：{{ props.fileName }} ({{ props.fileType }})</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { Loading, Warning, Document, ZoomIn, ZoomOut } from '@element-plus/icons-vue';

// 导入 vue-office 组件
import VueOfficeDocx from '@vue-office/docx';
import VueOfficeExcel from '@vue-office/excel';
import VueOfficePdf from '@vue-office/pdf';
import VueOfficePptx from '@vue-office/pptx';
import { fastApiRequest } from '/@/api/iot/knowledgeBase';

// 导入 vue-office 样式文件
import '@vue-office/docx/lib/index.css';
import '@vue-office/excel/lib/index.css';
// 注意：@vue-office/pdf 和 @vue-office/pptx 不需要CSS文件

// Props 定义
interface Props {
  src: string | ArrayBuffer | Blob;
  fileType?: string;
  fileName?: string;
  height?: string;
  width?: string;
}

const props = withDefaults(defineProps<Props>(), {
  fileType: '',
  fileName: '',
  height: '100%',
  width: '100%'
});

// Emits 定义
const emit = defineEmits<{
  rendered: [];
  error: [error: string];
  documentTypeDetected: [type: 'portrait' | 'landscape']; // 新增：文档方向检测
}>();

// 文档方向类型 - 更智能的判断
const documentOrientation = computed(() => {
  switch (previewType.value) {
    case 'docx':
    case 'pdf':
      return 'portrait'; // 竖向文档
    case 'excel':
      // Excel根据内容判断，默认为竖向（因为很多Excel表格都是竖向的）
      return 'portrait';
    case 'pptx':
      return 'landscape'; // 横向文档
    default:
      return 'portrait';
  }
});

// 响应式数据
const loading = ref(false);
const error = ref('');
const documentSrc = ref<string | ArrayBuffer | Blob>('');
const isLoading = ref(false); // 防止重复加载
const isReady = ref(false); // DOM是否准备就绪
const renderKey = ref(0); // 强制重新渲染的key

// Excel缩放控制
const excelZoom = ref(1.0); // Excel缩放比例

// PowerPoint专用状态管理
const pptxLoading = ref(false); // PowerPoint加载状态
const pptxProgress = ref(0); // PowerPoint加载进度

// PDF组件配置
const pdfOptions = computed(() => ({
  scale: 1.5, // 增加默认缩放比例，提高清晰度
  enableTextSelection: true, // 启用文本选择
  enableAnnotations: true, // 启用注释
  renderTextLayer: true, // 渲染文本层，提高文字清晰度
  renderAnnotationLayer: true, // 渲染注释层
}));

// Excel组件配置
const excelOptions = computed(() => ({
  // 尝试自适应内容大小
  fitToWidth: true,
  fitToHeight: true,
  // 设置初始缩放
  scale: 1.2,
  // 显示网格线
  showGridLines: true,
  // 显示行列标题
  showRowColHeaders: true,
}));

// 根据官方GitHub示例，Excel组件不需要options配置
// 官方示例非常简单，直接使用src即可

// 获取正确的MIME类型
const getContentType = (previewType: string, fileName?: string): string => {
  const ext = fileName?.toLowerCase().split('.').pop();

  switch (previewType) {
    case 'excel':
      return ext === 'xls' ? 'application/vnd.ms-excel' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    case 'pptx':
      return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
    case 'docx':
      return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    case 'pdf':
      return 'application/pdf';
    default:
      return 'application/octet-stream';
  }
};

// 计算属性
const previewType = computed(() => {
  const fileType = props.fileType?.toLowerCase() || '';
  const fileName = props.fileName?.toLowerCase() || '';

  // 简化调试信息
  console.log('文件类型检测:', { fileName, fileType });

  // 优先根据文件名判断预览类型（更准确）
  // Excel 文档 - 优先检测
  if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
    console.log('检测为 Excel 文档 (文件名)');
    return 'excel';
  }

  // PowerPoint 文档 - 优先检测
  if (fileName.endsWith('.pptx') || fileName.endsWith('.ppt')) {
    console.log('检测为 PowerPoint 文档 (文件名)');
    return 'pptx';
  }

  // Word 文档 - 优先检测
  if (fileName.endsWith('.docx') || fileName.endsWith('.doc')) {
    console.log('检测为 Word 文档 (文件名)');
    return 'docx';
  }

  // PDF 文档 - 优先检测
  if (fileName.endsWith('.pdf')) {
    console.log('检测为 PDF 文档 (文件名)');
    return 'pdf';
  }

  // 如果文件名检测失败，回退到fileType检测
  if (fileType.includes('excel') || fileType.includes('xlsx') || fileType.includes('xls')) {
    console.log('检测为 Excel 文档 (类型)');
    return 'excel';
  }

  if (fileType.includes('powerpoint') || fileType.includes('pptx') || fileType.includes('ppt')) {
    console.log('检测为 PowerPoint 文档 (类型)');
    return 'pptx';
  }

  if (fileType.includes('word') || fileType.includes('docx') || fileType.includes('doc')) {
    console.log('检测为 Word 文档 (类型)');
    return 'docx';
  }

  if (fileType.includes('pdf')) {
    console.log('检测为 PDF 文档 (类型)');
    return 'pdf';
  }

  console.log('未识别的文件类型');
  return 'unsupported';
});

const previewStyle = computed(() => {
  // 简化样式计算，主要通过CSS类控制
  return {
    width: props.width || '100%',
    height: props.height || '100%'
  };
});

// Excel容器样式
const excelContainerStyle = computed(() => {
  if (previewType.value === 'excel') {
    return {
      transform: `scale(${excelZoom.value})`,
      transformOrigin: 'top left',
      width: `${100 / excelZoom.value}%`,
      height: `${100 / excelZoom.value}%`
    };
  }
  return {};
});

// 方法
const loadDocument = async () => {
  if (!props.src) {
    error.value = '缺少文档源';
    return;
  }

  // 防止重复加载
  if (isLoading.value) {
    console.log('文档正在加载中，跳过重复请求');
    return;
  }

  isLoading.value = true;
  loading.value = true;
  error.value = '';

  // PowerPoint特殊处理：启动加载状态和进度模拟
  if (previewType.value === 'pptx') {
    pptxLoading.value = true;
    pptxProgress.value = 0;

    // 模拟PowerPoint加载进度
    const progressInterval = setInterval(() => {
      if (pptxProgress.value < 90) {
        pptxProgress.value += Math.random() * 15;
      } else {
        clearInterval(progressInterval);
      }
    }, 200);
  }

  // 强制重新渲染组件
  renderKey.value++;

  try {
    console.log('设置文档源:', props.src);
    console.log('文档类型:', previewType.value);

    // 根据官方示例分析，vue-office组件最适合直接使用URL字符串
    // 但我们的API需要认证，所以继续使用ArrayBuffer方式，但要优化配置

    if (typeof props.src === 'string' && (props.src.startsWith('http') || props.src.startsWith('/'))) {
      console.log('获取文档内容并创建Blob URL');
      console.log('文档类型:', previewType.value);

      // 检查文件格式支持
      if (previewType.value === 'excel' && props.fileName?.toLowerCase().endsWith('.xls')) {
        console.warn('警告：vue-office目前不支持.xls文件，只支持.xlsx文件');
        throw new Error('不支持.xls文件预览，请使用.xlsx格式');
      }

      // 获取文档内容
      const response = await fastApiRequest.get(props.src, {
        responseType: 'arraybuffer'
      });

      console.log('文档获取成功，大小:', response.data.byteLength);

      // 创建Blob URL，让vue-office像访问静态文件一样访问
      const blob = new Blob([response.data], {
        type: getContentType(previewType.value, props.fileName)
      });

      // 清理之前的Blob URL
      if (documentSrc.value && typeof documentSrc.value === 'string' && documentSrc.value.startsWith('blob:')) {
        URL.revokeObjectURL(documentSrc.value);
      }

      // 创建新的Blob URL
      const blobUrl = URL.createObjectURL(blob);
      console.log('创建Blob URL成功:', blobUrl);

      documentSrc.value = blobUrl;
    } else {
      // 直接使用传入的数据
      console.log('直接使用传入的数据:', typeof props.src);
      documentSrc.value = props.src;
    }
  } catch (err) {
    error.value = `加载文档失败: ${err instanceof Error ? err.message : '未知错误'}`;
    emit('error', error.value);
  } finally {
    loading.value = false;
    isLoading.value = false; // 重置加载状态，允许下次加载
  }
};

// 事件处理
const handleRendered = () => {
  loading.value = false;
  console.log('Vue Office 文档渲染完成');
  console.log('文档类型:', previewType.value);
  console.log('文档方向:', documentOrientation.value);

  // 对于Excel，尝试优化显示
  if (previewType.value === 'excel') {
    nextTick(() => {
      optimizeExcelDisplay();
    });
  }

  // 发出文档类型检测事件
  emit('documentTypeDetected', documentOrientation.value);
  emit('rendered');
};

// PowerPoint专用渲染处理
const handlePptxRendered = () => {
  console.log('PowerPoint 文档渲染成功');
  pptxLoading.value = false;
  pptxProgress.value = 100;
  loading.value = false;

  // 发出文档类型检测事件
  emit('documentTypeDetected', documentOrientation.value);
  emit('rendered');
};

// 优化Excel显示
const optimizeExcelDisplay = () => {
  try {
    // 查找Excel容器
    const excelContainer = document.querySelector('.preview-excel .vue-office-excel');
    if (excelContainer) {
      console.log('正在优化Excel显示...');

      // 添加自定义样式来改善显示
      const style = document.createElement('style');
      style.textContent = `
        .preview-excel .vue-office-excel {
          display: flex !important;
          justify-content: flex-start !important;
          align-items: flex-start !important;
        }
        .preview-excel .vue-office-excel > div {
          width: auto !important;
          max-width: 100% !important;
        }
      `;
      document.head.appendChild(style);
    }
  } catch (error) {
    console.warn('Excel显示优化失败:', error);
  }
};

const handleError = (err: any) => {
  loading.value = false;

  // 按照官方示例的简单错误处理
  console.error('Vue Office 渲染失败:', err);
  console.error('文档类型:', previewType.value);
  console.error('文件名:', props.fileName);

  let errorMsg = `文档渲染失败: ${err?.message || '未知错误'}`;

  // 特殊错误提示
  if (previewType.value === 'excel' && props.fileName?.toLowerCase().endsWith('.xls')) {
    errorMsg = '不支持.xls文件预览，vue-office目前只支持.xlsx格式';
  }

  error.value = errorMsg;
  ElMessage.error(errorMsg);
  emit('error', errorMsg);
};

// Excel缩放控制函数
const adjustExcelZoom = (delta: number) => {
  const newZoom = excelZoom.value + delta;
  if (newZoom >= 0.5 && newZoom <= 2) {
    excelZoom.value = newZoom;
    console.log('Excel缩放调整为:', Math.round(newZoom * 100) + '%');
  }
};

const resetExcelZoom = () => {
  excelZoom.value = 1.0;
  console.log('Excel缩放重置为100%');
};

// 生命周期
onMounted(async () => {
  // 等待DOM渲染完成
  await nextTick();
  isReady.value = true;
  console.log('VueOfficePreview DOM准备就绪');
});

// 组件卸载时清理Blob URL
onUnmounted(() => {
  if (documentSrc.value && typeof documentSrc.value === 'string' && documentSrc.value.startsWith('blob:')) {
    console.log('清理Blob URL:', documentSrc.value);
    URL.revokeObjectURL(documentSrc.value);
  }
});

// 优化：统一监听关键props变化，避免重复请求
watch(() => ({
  src: props.src,
  fileName: props.fileName,
  fileType: props.fileType
}), (newProps, oldProps) => {
  // 只有当关键属性真正发生变化且src不为空时才重新加载
  if (newProps.src && (
    newProps.src !== oldProps?.src ||
    newProps.fileName !== oldProps?.fileName ||
    newProps.fileType !== oldProps?.fileType
  )) {
    console.log('文档属性变化，重新加载:', newProps);
    loadDocument();
  }
}, { immediate: true, deep: true });
</script>

<style scoped>
.vue-office-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.preview-loading,
.preview-error,
.unsupported-preview {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  color: #909399;
  padding: 40px;
  text-align: center;
}

.waiting-content {
  flex: 1;
  width: 100%;
  height: 100%;
  background: #ffffff;
}

.office-preview-container {
  flex: 1;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

/* 基础样式设置 */
.office-preview-container :deep(*) {
  box-sizing: border-box;
}

/* Excel工具栏样式 */
.excel-toolbar {
  padding: 8px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.excel-zoom-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.zoom-tip {
  font-size: 12px;
  color: #909399;
}

/* Excel 预览样式优化 - 支持缩放和自适应 */
.preview-excel {
  background: #f8f9fa;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.excel-viewer {
  flex: 1;
  width: 100% !important;
  height: 100% !important;
  overflow: auto; /* 允许滚动查看大表格 */
}

/* Excel组件样式优化 */
.preview-excel :deep(.vue-office-excel) {
  height: 100% !important;
  width: 100% !important;
  display: flex;
  justify-content: flex-start; /* 左对齐，减少右侧空白 */
  align-items: flex-start;
}

.preview-excel :deep(.vue-office-excel > div) {
  height: 100% !important;
  width: auto !important; /* 自适应内容宽度 */
  min-width: 100%;
}

/* Excel工作表容器优化 - 支持缩放 */
.preview-excel :deep(.vue-office-excel iframe) {
  width: 100% !important;
  height: 100% !important;
  border: none;
  transform-origin: top left;
}

/* 缩放时的容器调整 */
.preview-excel.scaled {
  overflow: auto;
}

.preview-excel.scaled :deep(.vue-office-excel) {
  overflow: visible;
}

/* PowerPoint 预览样式优化 - 充满整个容器 */
.preview-pptx {
  background: #ffffff;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pptx-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #ffffff; /* 白色背景，避免黑色显示 */
  display: flex;
  flex-direction: column;
}

.pptx-viewer {
  width: 100% !important;
  height: 100% !important;
  background: #ffffff !important; /* 强制白色背景 */
  flex: 1;
  display: flex !important;
}

/* PowerPoint组件样式优化 - 确保充满整个容器 */
.preview-pptx :deep(.vue-office-pptx) {
  width: 100% !important;
  height: 100% !important;
  background: #ffffff !important; /* 强制白色背景 */
  display: flex !important;
  flex-direction: column !important;
}

.preview-pptx :deep(.vue-office-pptx > div) {
  width: 100% !important;
  height: 100% !important;
  background: #ffffff !important; /* 强制白色背景 */
  flex: 1 !important;
  display: flex !important;
}

/* 深度样式覆盖 - 确保PowerPoint内容充满容器并居中显示 */
.preview-pptx :deep(.vue-office-pptx canvas),
.preview-pptx :deep(.vue-office-pptx iframe),
.preview-pptx :deep(.vue-office-pptx .pptx-content) {
  width: 100% !important;
  height: 100% !important;
  max-width: 100% !important;
  max-height: 100% !important;
  object-fit: contain; /* 保持比例，适应容器 */
  display: block !important;
  margin: 0 auto !important; /* 水平居中 */
}

/* 确保PowerPoint容器内容居中 */
.preview-pptx :deep(.vue-office-pptx) {
  justify-content: center !important;
  align-items: center !important;
}

/* 移除可能的左侧偏移 */
.preview-pptx :deep(.vue-office-pptx *) {
  box-sizing: border-box !important;
}

/* PowerPoint加载覆盖层样式 */
.pptx-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #ffffff; /* 纯白色背景，完全覆盖黑色 */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  border-radius: 4px; /* 与容器保持一致 */
}

.pptx-loading-content {
  text-align: center;
  padding: 40px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 90%;
}

.pptx-loading-content h3 {
  margin: 16px 0 8px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 500;
}

.pptx-loading-content p {
  margin: 0 0 20px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.loading-progress {
  margin-top: 16px;
}

/* Word 预览样式优化 - 竖向文档，减少左右边距 */
.preview-docx {
  background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 10px; /* 减少边距 */
  height: 100%;
}

.docx-viewer {
  max-width: 95%; /* 增加宽度利用率 */
  width: 100%;
  background: #ffffff;
  border-radius: 4px;
  overflow: auto;
  height: fit-content;
  min-height: 500px;
}

/* Word组件样式优化 */
.preview-docx :deep(.vue-office-docx) {
  background: #ffffff !important;
}

/* PDF 预览样式优化 - 竖向文档，减少左右边距，增加缩放 */
.preview-pdf {
  background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 10px; /* 减少边距 */
  height: 100%;
}

.pdf-viewer {
  max-width: 95%; /* 增加宽度利用率 */
  width: 100%;
  background: #ffffff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: auto;
  height: fit-content;
  min-height: 500px;
}

/* PDF组件深度样式覆盖 - 优化显示效果 */
.preview-pdf :deep(.vue-office-pdf) {
  background: #ffffff !important;
  border: none !important;
}

.preview-pdf :deep(.vue-office-pdf canvas) {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
  /* PDF页面自动适应容器宽度 */
  max-width: 100%;
  height: auto;
}

.unsupported-preview h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 500;
}

.unsupported-preview p {
  margin: 0 0 8px 0;
  color: #909399;
  font-size: 14px;
}

.unsupported-preview .file-info {
  margin-top: 16px;
  font-size: 12px;
  color: #c0c4cc;
  font-family: monospace;
}
</style>
