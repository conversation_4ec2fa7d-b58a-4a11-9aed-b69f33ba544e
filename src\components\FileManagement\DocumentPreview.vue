<template>
  <div class="document-preview-container">

    <!-- 预览内容区域 -->
    <div
      ref="previewContainer"
      class="preview-content"
    >
      <!-- 加载状态 -->
      <div v-if="(loading || previewType === 'loading') && previewType !== 'office'" class="preview-loading">
        <el-icon class="is-loading" :size="48">
          <Loading />
        </el-icon>
        <p>正在加载文档预览...</p>
        <p class="loading-hint">请稍候，正在处理 {{ props.document.name }}</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="preview-error">
        <el-icon :size="48" color="#F56C6C">
          <Warning />
        </el-icon>
        <p>{{ error }}</p>
        <el-button type="primary" @click="loadPreview">重新加载</el-button>
      </div>

      <!-- 预览内容 -->
      <div v-else class="preview-viewer">
        <!-- 图片预览 -->
        <div v-if="previewType === 'image'" class="image-viewer">
          <div class="image-toolbar">
            <el-button-group>
              <el-button :icon="ZoomOut" @click="zoomOut" :disabled="scale <= 0.1" />
              <el-button @click="resetZoom">{{ Math.round(scale * 100) }}%</el-button>
              <el-button :icon="ZoomIn" @click="zoomIn" :disabled="scale >= 5" />
            </el-button-group>
            <el-button :icon="RefreshLeft" @click="rotateLeft" />
            <el-button :icon="RefreshRight" @click="rotateRight" />
          </div>
          
          <div class="image-content">
            <img
              :src="previewUrl"
              :style="imageStyle"
              @load="handleImageLoad"
              @error="handleImageError"
            />
          </div>
        </div>
        
        <!-- 文本预览 -->
        <div v-else-if="previewType === 'text'" class="text-viewer">
          <div class="text-toolbar">
            <el-button-group>
              <el-button @click="decreaseFontSize" :disabled="fontSize <= 12">A-</el-button>
              <el-button @click="resetFontSize">{{ fontSize }}px</el-button>
              <el-button @click="increaseFontSize" :disabled="fontSize >= 24">A+</el-button>
            </el-button-group>
            <el-switch
              v-model="wordWrap"
              active-text="自动换行"
              inactive-text="不换行"
            />
          </div>
          
          <div class="text-content">
            <pre
              :style="textStyle"
              v-html="highlightedText"
            />
          </div>
        </div>
        
        <!-- Office 文档预览 - 使用 vue-office -->
        <div v-else-if="previewType === 'office'" class="office-viewer" :class="`orientation-${documentOrientation}`">
          <VueOfficePreview
            :src="previewUrl"
            :file-type="props.document.type"
            :file-name="props.document.name"
            height="100%"
            @rendered="handleOfficeRendered"
            @error="handleOfficeError"
            @document-type-detected="handleDocumentTypeDetected"
          />
        </div>
        
        <!-- 不支持的文件类型 -->
        <div v-else-if="previewType === 'unsupported'" class="unsupported-viewer">
          <el-icon :size="64" color="#909399">
            <Document />
          </el-icon>
          <h3>此文件格式暂不支持在线预览</h3>
          <p class="file-info">
            文件名：{{ props.document.name }}<br>
            文件类型：{{ props.document.type || '未知' }}<br>
            文件大小：{{ formatFileSize(props.document.size || 0) }}
          </p>
          <p class="suggestion">建议下载文件后使用相应的应用程序打开查看</p>
          <div class="action-buttons">
            <el-button type="primary" @click="downloadDocument" :loading="downloadLoading">
              <el-icon><Download /></el-icon>
              下载文件
            </el-button>
            <el-button @click="loadPreview">
              <el-icon><Refresh /></el-icon>
              重新尝试
            </el-button>
          </div>
        </div>

        <!-- 默认加载状态（处理意外情况） -->
        <div v-else class="preview-loading">
          <el-icon class="is-loading" :size="48">
            <Loading />
          </el-icon>
          <p>正在处理文档...</p>
          <p class="loading-hint">预览类型：{{ previewType }}</p>
        </div>
      </div>
    </div>
    
    <!-- 预览设置面板 -->
    <div v-if="showSettings" class="preview-settings">
      <el-card>
        <template #header>
          <span>预览设置</span>
        </template>
        
        <el-form label-width="80px" size="small">
          <el-form-item label="主题">
            <el-select v-model="theme" @change="applyTheme">
              <el-option label="浅色" value="light" />
              <el-option label="深色" value="dark" />
              <el-option label="护眼" value="sepia" />
            </el-select>
          </el-form-item>
          
          <el-form-item v-if="previewType === 'text'" label="字体">
            <el-select v-model="fontFamily" @change="applyFont">
              <el-option label="系统默认" value="system" />
              <el-option label="等宽字体" value="monospace" />
              <el-option label="宋体" value="SimSun" />
              <el-option label="微软雅黑" value="Microsoft YaHei" />
            </el-select>
          </el-form-item>
          

        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import {
  Document,
  Loading,
  Warning,
  ZoomIn,
  ZoomOut,
  RefreshLeft,
  RefreshRight,
  Download,
  Refresh
} from '@element-plus/icons-vue';

import { type DocumentInfo } from '/@/api/iot/document';
import { Session } from '/@/utils/storage';
import { fastApiRequest } from '/@/api/iot/knowledgeBase';
import VueOfficePreview from './VueOfficePreview.vue';

// Props
interface Props {
  document: DocumentInfo;
  visible?: boolean;
  showSettings?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  showSettings: false
});

// Emits
const emit = defineEmits<{
  download: [document: DocumentInfo];
  documentTypeDetected: [type: 'portrait' | 'landscape']; // 新增：文档方向检测
}>();

// 文档方向状态
const documentOrientation = ref<'portrait' | 'landscape'>('portrait');

// 响应式数据
const loading = ref(false);
const error = ref('');
const downloadLoading = ref(false);
const previewUrl = ref('');
const previewContainer = ref<HTMLElement>();

// 预览控制
const scale = ref(1);
const rotation = ref(0);

const fontSize = ref(14);
const wordWrap = ref(true);

// 设置
const theme = ref('light');
const fontFamily = ref('system');


// 文本内容
const textContent = ref('');

// 下载URL（保留用于文件下载功能）
const downloadUrl = ref('');

// 计算属性
const previewType = computed(() => {
  // 如果正在加载，返回loading状态，避免显示"暂不支持"
  if (loading.value) return 'loading';

  // 优先根据实际加载的内容判断
  if (textContent.value) return 'text';
  if (previewUrl.value) {
    // 根据已加载的内容类型判断
    const type = props.document.type?.toLowerCase() || '';
    const fileName = props.document.name?.toLowerCase() || '';

    // 图片文件
    if (type.includes('image') || ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].some(ext => fileName.endsWith(`.${ext}`))) {
      return 'image';
    }

    // Office 文档
    if (type.includes('word') || type.includes('excel') || type.includes('pdf') || type.includes('powerpoint') ||
        type.includes('docx') || type.includes('xlsx') || type.includes('pptx') ||
        ['docx', 'doc', 'xlsx', 'xls', 'pdf', 'pptx', 'ppt'].some(ext => fileName.endsWith(`.${ext}`))) {
      return 'office';
    }
  }

  // 根据文档类型预判断预览类型（但只有在有内容时才显示）
  const type = props.document.type?.toLowerCase() || '';
  const fileName = props.document.name?.toLowerCase() || '';

  // 文本文件
  if (type.includes('text') || ['txt', 'md', 'json', 'xml', 'csv', 'yml', 'yaml', 'log', 'ini', 'conf', 'cfg', 'properties', 'sh', 'bat', 'ps1', 'py', 'js', 'ts', 'html', 'css', 'scss', 'less', 'sql'].some(ext => fileName.endsWith(`.${ext}`))) {
    return textContent.value ? 'text' : 'loading';
  }

  // 图片文件
  if (type.includes('image') || ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].some(ext => fileName.endsWith(`.${ext}`))) {
    return previewUrl.value ? 'image' : 'loading';
  }

  // Office 文档 - 使用 vue-office 预览 (支持 Word、Excel、PDF、PowerPoint)
  // 总是返回 'office'，让 VueOfficePreview 组件自己处理加载状态
  if (type.includes('word') || type.includes('excel') || type.includes('pdf') || type.includes('powerpoint') ||
      type.includes('docx') || type.includes('xlsx') || type.includes('pptx') ||
      ['docx', 'doc', 'xlsx', 'xls', 'pdf', 'pptx', 'ppt'].some(ext => fileName.endsWith(`.${ext}`))) {
    return 'office';
  }

  // 只有在确实不支持且不在加载中时才返回unsupported
  return 'unsupported';
});



const imageStyle = computed(() => ({
  transform: `scale(${scale.value}) rotate(${rotation.value}deg)`,
  transition: 'transform 0.3s ease',
  maxWidth: '100%',
  maxHeight: '100%'
}));

const textStyle = computed(() => ({
  fontSize: `${fontSize.value}px`,
  fontFamily: getFontFamily(),
  whiteSpace: wordWrap.value ? 'pre-wrap' : 'pre',
  lineHeight: '1.6',
  padding: '20px',
  margin: 0,
  background: getThemeBackground(),
  color: getThemeColor()
}));

const highlightedText = computed(() => {
  if (!textContent.value) return '';

  const fileName = props.document.name?.toLowerCase() || '';
  let content = textContent.value;

  // 简单的语法高亮处理
  if (fileName.endsWith('.json')) {
    // JSON 高亮
    try {
      const parsed = JSON.parse(content);
      content = JSON.stringify(parsed, null, 2);
      content = content
        .replace(/(".*?")\s*:/g, '<span style="color: #0066cc;">$1</span>:')
        .replace(/:\s*(".*?")/g, ': <span style="color: #009900;">$1</span>')
        .replace(/:\s*(\d+)/g, ': <span style="color: #ff6600;">$1</span>')
        .replace(/:\s*(true|false|null)/g, ': <span style="color: #cc0066;">$1</span>');
    } catch (e) {
      // 如果不是有效的JSON，保持原样
    }
  } else if (fileName.endsWith('.yml') || fileName.endsWith('.yaml')) {
    // YAML 高亮
    content = content
      .replace(/^(\s*)([\w-]+)(\s*:)/gm, '$1<span style="color: #0066cc;">$2</span>$3')
      .replace(/:\s*(".*?")/g, ': <span style="color: #009900;">$1</span>')
      .replace(/:\s*(\d+)/g, ': <span style="color: #ff6600;">$1</span>')
      .replace(/:\s*(true|false|null)/g, ': <span style="color: #cc0066;">$1</span>')
      .replace(/^(\s*#.*$)/gm, '<span style="color: #999999;">$1</span>');
  } else if (fileName.endsWith('.md')) {
    // Markdown 高亮
    content = content
      .replace(/^(#{1,6})\s+(.*)$/gm, '<span style="color: #0066cc; font-weight: bold;">$1</span> <span style="color: #333; font-weight: bold;">$2</span>')
      .replace(/\*\*(.*?)\*\*/g, '<span style="font-weight: bold;">$1</span>')
      .replace(/\*(.*?)\*/g, '<span style="font-style: italic;">$1</span>')
      .replace(/`(.*?)`/g, '<span style="background: #f5f5f5; padding: 2px 4px; border-radius: 3px; font-family: monospace;">$1</span>')
      .replace(/^\s*[-*+]\s+(.*)$/gm, '<span style="color: #666;">•</span> $1')
      .replace(/^\s*\d+\.\s+(.*)$/gm, '<span style="color: #666;">1.</span> $1');
  } else if (fileName.endsWith('.xml')) {
    // XML 高亮
    content = content
      .replace(/(&lt;\/?)(\w+)([^&gt;]*&gt;)/g, '$1<span style="color: #0066cc;">$2</span>$3')
      .replace(/(\w+)(=)(".*?")/g, '<span style="color: #cc0066;">$1</span>$2<span style="color: #009900;">$3</span>');
  } else if (fileName.endsWith('.css') || fileName.endsWith('.scss') || fileName.endsWith('.less')) {
    // CSS 高亮
    content = content
      .replace(/([\w-]+)\s*{/g, '<span style="color: #0066cc;">$1</span> {')
      .replace(/([\w-]+)\s*:/g, '<span style="color: #cc0066;">$1</span>:')
      .replace(/:\s*([^;]+);/g, ': <span style="color: #009900;">$1</span>;')
      .replace(/\/\*.*?\*\//g, '<span style="color: #999999;">$&</span>');
  }

  // HTML 转义（防止XSS）
  if (!content.includes('<span')) {
    content = content
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  return content;
});

// 方法
const loadPreview = async () => {
  if (!props.document.id) return;

  // 防止重复调用
  if (loading.value) {
    console.log('DocumentPreview: 正在加载中，跳过重复请求');
    return;
  }

  // 重置状态
  loading.value = true;
  error.value = '';
  textContent.value = '';
  previewUrl.value = '';
  downloadUrl.value = '';

  try {
    // 获取token
    const token = Session.get('token');
    if (!token) {
      error.value = '请先登录';
      console.warn('DocumentPreview: No token found, user needs to login');
      return;
    }

    // 调用新的预览API，传递文档名称作为参数
    const response = await fastApiRequest.get(`/api/iot/v1/documents/${props.document.kb_id}/${props.document.id}/preview`, {
      params: {
        doc_name: props.document.name || ''
      }
    });

    // 统一处理JSON响应
    const result = response.data;
    if (result.code === 200) {
      const data = result.data;
      console.log('预览数据:', data);
      console.log('文档名称:', props.document.name);
      console.log('返回的content_type:', data.content_type);

      // 根据content_type处理不同类型的文档
      switch (data.content_type) {
        case 'text/plain':
          if (data.content) {
            // 直接显示文本内容
            textContent.value = data.content;
            console.log('显示文本内容，长度:', data.content.length);
          } else if (data.url) {
            // 通过URL获取文本内容
            console.log('通过URL获取文本内容:', data.url);
            await loadTextContentFromUrl(data.url);
          } else {
            throw new Error('文本文档缺少内容或URL');
          }
          break;

        case 'application/pdf':
        case 'pdf':
          // PDF现在也使用vue-office预览，处理方式与office文档相同
          previewUrl.value = data.url || `/api/iot/v1/documents/${props.document.kb_id}/${props.document.id}/content`;
          console.log('PDF文档预览URL (content端点):', previewUrl.value);

          // 设置下载URL（使用download端点）
          downloadUrl.value = data.download_url || `/api/iot/v1/documents/${props.document.kb_id}/${props.document.id}/download`;
          console.log('PDF文档下载URL (download端点):', downloadUrl.value);
          break;

        case 'image':
          if (data.url) {
            previewUrl.value = data.url;
            console.log('设置图片预览URL:', data.url);
          } else {
            throw new Error('图片文档缺少预览URL');
          }
          break;

        case 'office':
          // 对于 Office 文档，使用后端返回的content URL（不带下载头）
          previewUrl.value = data.url || `/api/iot/v1/documents/${props.document.kb_id}/${props.document.id}/content`;
          console.log('Office文档预览URL (content端点):', previewUrl.value);

          // 设置下载URL（使用download端点）
          downloadUrl.value = data.download_url || `/api/iot/v1/documents/${props.document.kb_id}/${props.document.id}/download`;
          console.log('Office文档下载URL (download端点):', downloadUrl.value);
          break;

        default:
          // 未知类型或不支持的类型，尝试根据文件扩展名推断处理方式
          console.warn('未知的content_type:', data.content_type);
          console.log('文档信息:', {
            name: props.document.name,
            content_type: data.content_type,
            has_url: !!data.url,
            has_content: !!data.content,
            message: data.message
          });

          if (data.url) {
            // 根据文件扩展名尝试推断文件类型
            const fileName = props.document.name?.toLowerCase() || '';
            const fileExtension = fileName.split('.').pop() || '';

            console.log('尝试根据文件扩展名推断类型:', fileExtension);

            // 尝试作为Office文档处理（PDF、Word、Excel、PowerPoint）
            if (['pdf', 'docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt'].includes(fileExtension)) {
              console.log('根据扩展名推断为Office文档，设置预览URL');
              previewUrl.value = data.url;
              downloadUrl.value = data.download_url || `/api/iot/v1/documents/${props.document.kb_id}/${props.document.id}/download`;
            }
            // 尝试作为图片处理
            else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileExtension)) {
              console.log('根据扩展名推断为图片，设置预览URL');
              previewUrl.value = data.url;
            }
            // 尝试作为文本文件处理
            else if (['txt', 'md', 'json', 'xml', 'csv', 'yml', 'yaml', 'log', 'ini', 'conf', 'cfg', 'properties', 'sh', 'bat', 'ps1', 'py', 'js', 'ts', 'html', 'css', 'scss', 'less', 'sql'].includes(fileExtension)) {
              console.log('根据扩展名推断为文本文件，尝试加载文本内容');
              if (data.content) {
                textContent.value = data.content;
              } else {
                await loadTextContentFromUrl(data.url);
              }
            }
            // 真正不支持的文件类型
            else {
              console.log('无法根据扩展名推断文件类型，显示不支持提示');
              ElMessage.warning(data.message || '该文档格式不支持在线预览，请下载查看');
              // 不设置预览内容，让previewType返回'unsupported'
            }
          } else {
            throw new Error(data.message || '不支持的文档类型');
          }
          break;
      }
    } else {
      throw new Error(result.message || '预览失败');
    }
  } catch (err) {
    error.value = '加载预览失败';
    console.error('Preview load error:', err);
    // 只有在不是认证错误时才显示错误消息
    if (err instanceof Error && !err.message.includes('请先登录')) {
      ElMessage.error(`加载预览失败: ${err.message}`);
    }
  } finally {
    loading.value = false;
  }
};



const loadTextContentFromUrl = async (url: string) => {
  try {
    console.log('从URL加载文本内容:', url);

    // 获取token
    const token = Session.get('token');
    if (!token) {
      throw new Error('请先登录');
    }

    // 构建完整URL
    const fullUrl = url.startsWith('http') ? url : `${window.location.origin}${url}`;

    const response = await fetch(fullUrl, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept': 'text/plain, application/json, */*'
      }
    });

    if (response.ok) {
      const contentType = response.headers.get('content-type') || '';

      if (contentType.includes('application/json')) {
        // 如果返回的是JSON，可能是错误响应
        const jsonData = await response.json();
        if (jsonData.code === 200 && jsonData.data?.content) {
          textContent.value = jsonData.data.content;
        } else {
          throw new Error(jsonData.message || '获取文本内容失败');
        }
      } else {
        // 直接作为文本处理
        const text = await response.text();
        textContent.value = text;
        console.log('成功加载文本内容，长度:', text.length);
      }
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (err) {
    console.error('从URL加载文本内容失败:', err);
    error.value = `加载文本内容失败: ${err instanceof Error ? err.message : '未知错误'}`;
    ElMessage.error(`加载文本内容失败: ${err instanceof Error ? err.message : '未知错误'}`);
  }
};









const downloadDocument = async () => {
  downloadLoading.value = true;
  try {
    emit('download', props.document);

    // 获取token
    const token = Session.get('token');
    if (!token) {
      ElMessage.error('请先登录');
      return;
    }

    // 使用fastApiRequest下载文件
    const response = await fastApiRequest.get(`/api/iot/v1/documents/${props.document.kb_id}/${props.document.id}/download`, {
      responseType: 'blob'
    });

    const blob = response.data;
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = props.document.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    ElMessage.success('开始下载文件');
  } catch (err) {
    console.error('下载失败:', err);
    ElMessage.error(`下载失败: ${err instanceof Error ? err.message : '未知错误'}`);
  } finally {
    downloadLoading.value = false;
  }
};





// 缩放控制
const zoomIn = () => {
  if (previewType.value === 'image') {
    scale.value = Math.min(scale.value * 1.2, 5);
  } else {
    scale.value = Math.min(scale.value * 1.1, 3);
  }
};

const zoomOut = () => {
  if (previewType.value === 'image') {
    scale.value = Math.max(scale.value / 1.2, 0.1);
  } else {
    scale.value = Math.max(scale.value / 1.1, 0.5);
  }
};

const resetZoom = () => {
  scale.value = 1;
};

// 旋转控制
const rotateLeft = () => {
  rotation.value -= 90;
};

const rotateRight = () => {
  rotation.value += 90;
};

// 字体控制
const increaseFontSize = () => {
  fontSize.value = Math.min(fontSize.value + 2, 24);
};

const decreaseFontSize = () => {
  fontSize.value = Math.max(fontSize.value - 2, 12);
};

const resetFontSize = () => {
  fontSize.value = 14;
};

// 主题和样式
const getFontFamily = () => {
  const fontMap: Record<string, string> = {
    system: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    monospace: 'Monaco, Consolas, "Courier New", monospace',
    SimSun: 'SimSun, serif',
    'Microsoft YaHei': '"Microsoft YaHei", sans-serif'
  };
  return fontMap[fontFamily.value] || fontMap.system;
};

const getThemeBackground = () => {
  const bgMap: Record<string, string> = {
    light: '#ffffff',
    dark: '#1e1e1e',
    sepia: '#f4f1ea'
  };
  return bgMap[theme.value] || bgMap.light;
};

const getThemeColor = () => {
  const colorMap: Record<string, string> = {
    light: '#303133',
    dark: '#e4e7ed',
    sepia: '#5c4b37'
  };
  return colorMap[theme.value] || colorMap.light;
};

const applyTheme = () => {
  // 应用主题样式 - 功能待实现
};

const applyFont = () => {
  // 应用字体样式 - 功能待实现
};



const handleImageLoad = () => {
  // 图片加载完成事件处理
};

const handleImageError = () => {
  error.value = '图片加载失败';
};

const handleOfficeRendered = () => {
  console.log('Office 文档渲染完成');
};

const handleOfficeError = (errorMsg: string) => {
  console.error('Office 文档渲染失败:', errorMsg);
  error.value = errorMsg;
};

const handleDocumentTypeDetected = (type: 'portrait' | 'landscape') => {
  console.log('检测到文档方向:', type);
  documentOrientation.value = type;
  // 向父组件传递文档类型信息
  emit('documentTypeDetected', type);
};

// 工具函数
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 生命周期
onMounted(() => {
  // 检查是否有token再加载预览
  const token = Session.get('token');
  if (token) {
    loadPreview();
  } else {
    console.log('DocumentPreview: No token found, waiting for user login');
  }

});



// 监听文档变化
watch(() => props.document, () => {
  if (props.document.id) {
    const token = Session.get('token');
    if (token) {
      loadPreview();
    }
  }
}, { deep: true });

// 监听token变化（当用户登录后自动加载预览）
watch(() => Session.get('token'), (newToken) => {
  if (newToken && props.document.id && !previewUrl.value && !textContent.value) {
    loadPreview();
  }
});

// 监听可见性变化
watch(() => props.visible, (visible) => {
  if (visible && props.document.id) {
    loadPreview();
  }
});
</script>

<style scoped>
.document-preview-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
}





.document-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.document-details {
  flex: 1;
}

.document-name {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.document-meta {
  font-size: 12px;
  color: #909399;
  display: flex;
  gap: 8px;
  align-items: center;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.preview-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}



.preview-loading,
.preview-error,
.unsupported-viewer {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  color: #909399;
}

.preview-loading .loading-hint {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.unsupported-viewer .file-info {
  text-align: center;
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin: 16px 0;
}

.unsupported-viewer .suggestion {
  font-size: 14px;
  color: #909399;
  margin-bottom: 20px;
}

.unsupported-viewer .action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.preview-error .el-button {
  margin-top: 16px;
}

.preview-viewer {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.pdf-viewer,
.image-viewer,
.text-viewer,
.office-viewer {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 文档方向适配样式 */
.office-viewer.orientation-portrait {
  /* 竖向文档（PDF、Word）- 减少左右边距，增加内容区域 */
  padding: 0;
}

.office-viewer.orientation-landscape {
  /* 横向文档（Excel、PowerPoint）- 保持当前布局 */
  padding: 0;
}

.office-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.office-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #606266;
  font-weight: 500;
}



/* 已移除不再使用的 office-placeholder、office-options 和 converted-pdf-viewer 样式 */

.pdf-toolbar,
.image-toolbar,
.text-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid #ebeef5;
  background: #f8f9fa;
}

.pdf-controls,
.page-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-info {
  font-size: 14px;
  color: #606266;
  min-width: 60px;
  text-align: center;
}

.pdf-content,
.image-content,
.text-content {
  flex: 1;
  overflow: auto;
  position: relative;
}

.image-content {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
}

.text-content {
  background: #fff;
}

.preview-settings {
  position: absolute;
  top: 60px;
  right: 20px;
  width: 250px;
  z-index: 100;
}

@media (max-width: 768px) {
  .preview-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .document-info {
    justify-content: center;
  }
  
  .preview-actions {
    justify-content: center;
  }
  
  .pdf-toolbar,
  .image-toolbar,
  .text-toolbar {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
